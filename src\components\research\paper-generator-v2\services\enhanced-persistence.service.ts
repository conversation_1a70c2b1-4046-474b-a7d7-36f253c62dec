/**
 * Enhanced Persistence Service for Paper Generator V2
 * Provides auto-save, section-level tracking, and comprehensive paper history
 */

import { supabase } from '@/lib/supabase';
import { Paper, PaperSection } from '../types';
import { toast } from 'sonner';

// Section-level persistence types
export interface PaperSectionV2 {
  id: string;
  paperId: string;
  userId: string;
  sectionType: string;
  sectionTitle: string;
  sectionOrder: number;
  content: string;
  wordCount: number;
  characterCount: number;
  status: 'empty' | 'draft' | 'completed' | 'reviewed';
  aiGenerated: boolean;
  aiModel?: string;
  generationPrompt?: string;
  citations: any[];
  figures: any[];
  structure: any;
  version: number;
  parentVersionId?: string;
  createdAt: Date;
  updatedAt: Date;
  lastModified: Date;
}

export interface SectionHistoryEntry {
  id: string;
  sectionId: string;
  paperId: string;
  userId: string;
  content: string;
  wordCount: number;
  changeType: 'created' | 'edited' | 'ai_generated' | 'imported' | 'merged';
  changeDescription?: string;
  aiModel?: string;
  generationPrompt?: string;
  version: number;
  previousVersionId?: string;
  createdAt: Date;
}

export interface AutoSaveEntry {
  id: string;
  paperId: string;
  userId: string;
  paperData: Paper;
  sectionsData: PaperSection[];
  saveTrigger: 'timer' | 'section_update' | 'manual' | 'export';
  saveDescription?: string;
  createdAt: Date;
}

export class EnhancedPersistenceService {
  private readonly PAPERS_TABLE = 'paper_generations_v2';
  private readonly SECTIONS_TABLE = 'paper_sections_v2';
  private readonly HISTORY_TABLE = 'paper_section_history';
  private readonly AUTO_SAVE_TABLE = 'paper_auto_saves';
  
  private autoSaveInterval: NodeJS.Timeout | null = null;
  private pendingChanges: Map<string, PaperSection> = new Map();

  /**
   * Get current authenticated user
   */
  private async getCurrentUser() {
    const { data: { user }, error } = await supabase.auth.getUser();
    if (error || !user) {
      throw new Error('User not authenticated');
    }
    return user;
  }

  /**
   * Start auto-save for a paper
   */
  startAutoSave(paperId: string, paper: Paper, intervalMs: number = 30000) {
    console.log(`🔄 Auto-save: Starting for paper ${paperId} (interval: ${intervalMs}ms)`);
    
    this.stopAutoSave(); // Stop any existing auto-save
    
    this.autoSaveInterval = setInterval(async () => {
      if (this.pendingChanges.size > 0) {
        console.log(`💾 Auto-save: Saving ${this.pendingChanges.size} pending changes`);
        await this.autoSavePaper(paperId, paper, 'timer');
        this.pendingChanges.clear();
      }
    }, intervalMs);
  }

  /**
   * Stop auto-save
   */
  stopAutoSave() {
    if (this.autoSaveInterval) {
      console.log('⏹️ Auto-save: Stopping');
      clearInterval(this.autoSaveInterval);
      this.autoSaveInterval = null;
    }
  }

  /**
   * Mark section as having pending changes
   */
  markSectionChanged(sectionId: string, section: PaperSection) {
    this.pendingChanges.set(sectionId, section);
    console.log(`📝 Auto-save: Marked section "${section.title}" as changed`);
  }

  /**
   * Save paper to auto-save table
   */
  async autoSavePaper(
    paperId: string,
    paper: Paper,
    trigger: 'timer' | 'section_update' | 'manual' | 'export',
    description?: string
  ): Promise<void> {
    try {
      const user = await this.getCurrentUser();

      // Convert temporary paper ID to valid UUID
      const validPaperId = this.generateValidPaperId(paperId);

      console.log(`🔄 Auto-save: Saving paper (trigger: ${trigger})`, {
        paperId,
        validPaperId,
        title: paper.metadata.title,
        sectionCount: paper.sections.length,
        trigger
      });

      const { error } = await supabase
        .from(this.AUTO_SAVE_TABLE)
        .insert({
          paper_id: validPaperId,
          user_id: user.id,
          paper_data: paper,
          sections_data: paper.sections,
          save_trigger: trigger,
          save_description: description || `Auto-save triggered by ${trigger}`
        });

      if (error) {
        console.error('❌ Auto-save: Database error', error);
        throw error;
      }

      console.log(`✅ Auto-save: Paper saved successfully (trigger: ${trigger})`);

      // Keep only last 10 auto-saves per paper
      await this.cleanupOldAutoSaves(paperId);

    } catch (error) {
      console.error('❌ Auto-save: Failed to save paper', error);
      // Don't throw error to prevent breaking the UI
    }
  }

  /**
   * Clean up old auto-saves (keep only last 10)
   */
  private async cleanupOldAutoSaves(paperId: string): Promise<void> {
    try {
      const user = await this.getCurrentUser();

      // Get all auto-saves for this paper, ordered by creation date
      const { data: autoSaves, error: fetchError } = await supabase
        .from(this.AUTO_SAVE_TABLE)
        .select('id, created_at')
        .eq('paper_id', paperId)
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (fetchError) throw fetchError;

      // If more than 10, delete the oldest ones
      if (autoSaves && autoSaves.length > 10) {
        const toDelete = autoSaves.slice(10).map(save => save.id);
        
        const { error: deleteError } = await supabase
          .from(this.AUTO_SAVE_TABLE)
          .delete()
          .in('id', toDelete);

        if (deleteError) throw deleteError;
        
        console.log(`🧹 Auto-save: Cleaned up ${toDelete.length} old saves`);
      }
    } catch (error) {
      console.error('❌ Auto-save: Failed to cleanup old saves', error);
    }
  }

  /**
   * Convert temporary paper ID to valid UUID
   */
  private generateValidPaperId(tempId: string): string {
    // If it's already a valid UUID, return as is
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (uuidRegex.test(tempId)) {
      return tempId;
    }

    // For temporary IDs like "temp_1755015262671", generate a deterministic UUID
    // This ensures the same temp ID always maps to the same UUID
    // Simple hash function for browser compatibility
    let hash = 0;
    for (let i = 0; i < tempId.length; i++) {
      const char = tempId.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }

    // Convert to positive number and pad with zeros
    const hashStr = Math.abs(hash).toString(16).padStart(8, '0');
    const timestamp = Date.now().toString(16).slice(-8);

    // Format as UUID v4
    return [
      hashStr.substr(0, 8),
      timestamp.substr(0, 4),
      '4' + hashStr.substr(1, 3), // Version 4
      '8' + timestamp.substr(1, 3), // Variant bits
      (hashStr + timestamp).substr(0, 12)
    ].join('-');
  }

  /**
   * Save individual section with history tracking
   */
  async saveSectionWithHistory(
    paperId: string,
    section: PaperSection,
    changeType: 'created' | 'edited' | 'ai_generated' | 'imported' | 'merged',
    changeDescription?: string,
    aiModel?: string,
    generationPrompt?: string
  ): Promise<PaperSectionV2 | null> {
    try {
      const user = await this.getCurrentUser();

      const wordCount = section.content.split(/\s+/).filter(w => w.length > 0).length;
      const characterCount = section.content.length;

      // Convert temporary paper ID to valid UUID
      const validPaperId = this.generateValidPaperId(paperId);

      // Prepare section data with proper validation
      const sectionData = {
        paper_id: validPaperId,
        user_id: user.id,
        section_type: section.type,
        section_title: section.title || 'Untitled Section',
        section_order: section.id ? parseInt(section.id.split('_').pop() || '0') : 0,
        content: section.content || '',
        word_count: wordCount,
        character_count: characterCount,
        status: section.status || 'draft',
        ai_generated: section.aiGenerated || false,
        ai_model: aiModel || null,
        generation_prompt: generationPrompt || null,
        citations: section.citations || [],
        figures: section.figures || [],
        structure: section.structure || {},
        version: 1,
        updated_at: new Date().toISOString(),
        last_modified: new Date().toISOString()
      };

      console.log('💾 Saving section to database:', {
        paperId,
        sectionType: section.type,
        sectionTitle: section.title,
        contentLength: section.content?.length || 0,
        wordCount,
        status: section.status
      });

      const { data: savedSection, error: sectionError } = await supabase
        .from(this.SECTIONS_TABLE)
        .upsert(sectionData, {
          onConflict: 'paper_id,section_type',
          ignoreDuplicates: false
        })
        .select()
        .single();

      if (sectionError) {
        console.error('❌ Section save error:', sectionError);
        console.error('❌ Section data that failed:', sectionData);
        throw sectionError;
      }

      console.log('✅ Section saved successfully:', savedSection.id);

      // Save to history
      const { error: historyError } = await supabase
        .from(this.HISTORY_TABLE)
        .insert({
          section_id: savedSection.id,
          paper_id: validPaperId,
          user_id: user.id,
          content: section.content,
          word_count: wordCount,
          change_type: changeType,
          change_description: changeDescription,
          ai_model: aiModel,
          generation_prompt: generationPrompt,
          version: savedSection.version
        });

      if (historyError) throw historyError;

      console.log(`✅ Section saved: "${section.title}" (${changeType})`);
      
      return this.transformSectionFromDB(savedSection);
      
    } catch (error) {
      console.error('❌ Failed to save section with history', error);
      toast.error('Failed to save section');
      return null;
    }
  }

  /**
   * Get section history
   */
  async getSectionHistory(sectionId: string): Promise<SectionHistoryEntry[]> {
    try {
      const user = await this.getCurrentUser();

      const { data, error } = await supabase
        .from(this.HISTORY_TABLE)
        .select('*')
        .eq('section_id', sectionId)
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) throw error;

      return data?.map(this.transformHistoryFromDB) || [];
      
    } catch (error) {
      console.error('❌ Failed to get section history', error);
      return [];
    }
  }

  /**
   * Get auto-save history for a paper
   */
  async getAutoSaveHistory(paperId: string, limit: number = 10): Promise<AutoSaveEntry[]> {
    try {
      const user = await this.getCurrentUser();

      const { data, error } = await supabase
        .from(this.AUTO_SAVE_TABLE)
        .select('*')
        .eq('paper_id', paperId)
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) throw error;

      return data?.map(this.transformAutoSaveFromDB) || [];
      
    } catch (error) {
      console.error('❌ Failed to get auto-save history', error);
      return [];
    }
  }

  /**
   * Transform section data from database format
   */
  private transformSectionFromDB(data: any): PaperSectionV2 {
    return {
      id: data.id,
      paperId: data.paper_id,
      userId: data.user_id,
      sectionType: data.section_type,
      sectionTitle: data.section_title,
      sectionOrder: data.section_order,
      content: data.content,
      wordCount: data.word_count,
      characterCount: data.character_count,
      status: data.status,
      aiGenerated: data.ai_generated,
      aiModel: data.ai_model,
      generationPrompt: data.generation_prompt,
      citations: data.citations || [],
      figures: data.figures || [],
      structure: data.structure || {},
      version: data.version,
      parentVersionId: data.parent_version_id,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at),
      lastModified: new Date(data.last_modified)
    };
  }

  /**
   * Transform history data from database format
   */
  private transformHistoryFromDB(data: any): SectionHistoryEntry {
    return {
      id: data.id,
      sectionId: data.section_id,
      paperId: data.paper_id,
      userId: data.user_id,
      content: data.content,
      wordCount: data.word_count,
      changeType: data.change_type,
      changeDescription: data.change_description,
      aiModel: data.ai_model,
      generationPrompt: data.generation_prompt,
      version: data.version,
      previousVersionId: data.previous_version_id,
      createdAt: new Date(data.created_at)
    };
  }

  /**
   * Transform auto-save data from database format
   */
  private transformAutoSaveFromDB(data: any): AutoSaveEntry {
    return {
      id: data.id,
      paperId: data.paper_id,
      userId: data.user_id,
      paperData: data.paper_data,
      sectionsData: data.sections_data,
      saveTrigger: data.save_trigger,
      saveDescription: data.save_description,
      createdAt: new Date(data.created_at)
    };
  }
}

// Export singleton instance
export const enhancedPersistenceService = new EnhancedPersistenceService();
