-- Paper Generator V2 Section-Level Persistence Schema
-- Extends the existing schema with detailed section tracking and history

-- Paper Sections table for granular section management
CREATE TABLE IF NOT EXISTS public.paper_sections_v2 (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    paper_id UUID REFERENCES public.paper_generations_v2(id) ON DELETE CASCADE NOT NULL,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    
    -- Section identification
    section_type TEXT NOT NULL, -- 'title', 'abstract', 'introduction', etc.
    section_title TEXT NOT NULL,
    section_order INTEGER NOT NULL DEFAULT 0,
    
    -- Content and metadata
    content TEXT NOT NULL DEFAULT '',
    word_count INTEGER DEFAULT 0,
    character_count INTEGER DEFAULT 0,
    
    -- Section status and generation info
    status TEXT NOT NULL DEFAULT 'empty' CHECK (status IN ('empty', 'draft', 'completed', 'reviewed')),
    ai_generated BOOLEAN DEFAULT FALSE,
    ai_model TEXT,
    generation_prompt TEXT,
    
    -- Citations and references
    citations JSONB DEFAULT '[]', -- Array of citation objects
    figures JSONB DEFAULT '[]', -- Array of figure objects
    
    -- Content structure metadata
    structure JSONB DEFAULT '{}', -- Heading counts, paragraph counts, etc.
    
    -- Version control
    version INTEGER DEFAULT 1,
    parent_version_id UUID REFERENCES public.paper_sections_v2(id) ON DELETE SET NULL,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_modified TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT paper_sections_v2_title_length CHECK (char_length(section_title) >= 1 AND char_length(section_title) <= 200),
    CONSTRAINT paper_sections_v2_content_length CHECK (char_length(content) <= 50000),
    CONSTRAINT paper_sections_v2_word_count_positive CHECK (word_count >= 0),
    CONSTRAINT paper_sections_v2_version_positive CHECK (version >= 1)
);

-- Paper Section History table for tracking all changes
CREATE TABLE IF NOT EXISTS public.paper_section_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    section_id UUID REFERENCES public.paper_sections_v2(id) ON DELETE CASCADE NOT NULL,
    paper_id UUID REFERENCES public.paper_generations_v2(id) ON DELETE CASCADE NOT NULL,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    
    -- Historical content
    content TEXT NOT NULL,
    word_count INTEGER DEFAULT 0,
    
    -- Change metadata
    change_type TEXT NOT NULL CHECK (change_type IN ('created', 'edited', 'ai_generated', 'imported', 'merged')),
    change_description TEXT,
    ai_model TEXT, -- If AI generated
    generation_prompt TEXT, -- If AI generated
    
    -- Version info
    version INTEGER NOT NULL,
    previous_version_id UUID REFERENCES public.paper_section_history(id) ON DELETE SET NULL,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT paper_section_history_content_length CHECK (char_length(content) <= 50000),
    CONSTRAINT paper_section_history_word_count_positive CHECK (word_count >= 0),
    CONSTRAINT paper_section_history_version_positive CHECK (version >= 1)
);

-- Paper Auto-Save table for draft persistence
CREATE TABLE IF NOT EXISTS public.paper_auto_saves (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    paper_id UUID REFERENCES public.paper_generations_v2(id) ON DELETE CASCADE NOT NULL,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    
    -- Complete paper state
    paper_data JSONB NOT NULL, -- Full paper object
    sections_data JSONB NOT NULL, -- All sections data
    
    -- Auto-save metadata
    save_trigger TEXT NOT NULL CHECK (save_trigger IN ('timer', 'section_update', 'manual', 'export')),
    save_description TEXT,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT paper_auto_saves_data_not_empty CHECK (jsonb_array_length(sections_data) >= 0)
);

-- Unique constraints for upsert operations
ALTER TABLE public.paper_sections_v2 ADD CONSTRAINT IF NOT EXISTS paper_sections_v2_paper_section_unique
    UNIQUE (paper_id, section_type);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_paper_sections_v2_paper_id ON public.paper_sections_v2(paper_id);
CREATE INDEX IF NOT EXISTS idx_paper_sections_v2_user_id ON public.paper_sections_v2(user_id);
CREATE INDEX IF NOT EXISTS idx_paper_sections_v2_section_type ON public.paper_sections_v2(section_type);
CREATE INDEX IF NOT EXISTS idx_paper_sections_v2_status ON public.paper_sections_v2(status);
CREATE INDEX IF NOT EXISTS idx_paper_sections_v2_updated_at ON public.paper_sections_v2(updated_at DESC);
CREATE INDEX IF NOT EXISTS idx_paper_sections_v2_word_count ON public.paper_sections_v2(word_count);

CREATE INDEX IF NOT EXISTS idx_paper_section_history_section_id ON public.paper_section_history(section_id);
CREATE INDEX IF NOT EXISTS idx_paper_section_history_paper_id ON public.paper_section_history(paper_id);
CREATE INDEX IF NOT EXISTS idx_paper_section_history_user_id ON public.paper_section_history(user_id);
CREATE INDEX IF NOT EXISTS idx_paper_section_history_change_type ON public.paper_section_history(change_type);
CREATE INDEX IF NOT EXISTS idx_paper_section_history_created_at ON public.paper_section_history(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_paper_section_history_version ON public.paper_section_history(version);

CREATE INDEX IF NOT EXISTS idx_paper_auto_saves_paper_id ON public.paper_auto_saves(paper_id);
CREATE INDEX IF NOT EXISTS idx_paper_auto_saves_user_id ON public.paper_auto_saves(user_id);
CREATE INDEX IF NOT EXISTS idx_paper_auto_saves_created_at ON public.paper_auto_saves(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_paper_auto_saves_save_trigger ON public.paper_auto_saves(save_trigger);

-- Row Level Security (RLS) Policies
ALTER TABLE public.paper_sections_v2 ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.paper_section_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.paper_auto_saves ENABLE ROW LEVEL SECURITY;

-- Paper Sections V2 RLS Policies
CREATE POLICY "Users can view their own paper sections" ON public.paper_sections_v2
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own paper sections" ON public.paper_sections_v2
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own paper sections" ON public.paper_sections_v2
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own paper sections" ON public.paper_sections_v2
    FOR DELETE USING (auth.uid() = user_id);

-- Paper Section History RLS Policies
CREATE POLICY "Users can view their own section history" ON public.paper_section_history
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own section history" ON public.paper_section_history
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Paper Auto-Saves RLS Policies
CREATE POLICY "Users can view their own auto-saves" ON public.paper_auto_saves
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own auto-saves" ON public.paper_auto_saves
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own auto-saves" ON public.paper_auto_saves
    FOR DELETE USING (auth.uid() = user_id);
